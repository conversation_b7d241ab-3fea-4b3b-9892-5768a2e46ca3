//! Drawing utilities for the CS2 overlay
//! 
//! This module provides basic drawing functions for rendering cheat overlays.
//! All drawing is done in BGRA format (Blue, Green, Red, Alpha) as required by Wayland.

/// Represents a color in BGRA format
#[derive(Debug, Clone, Copy)]
pub struct Color {
    pub b: u8,
    pub g: u8,
    pub r: u8,
    pub a: u8,
}

impl Color {
    /// Create a new color from RGBA values
    pub fn rgba(r: u8, g: u8, b: u8, a: u8) -> Self {
        Self { r, g, b, a }
    }

    /// Create a transparent color
    pub fn transparent() -> Self {
        Self { r: 0, g: 0, b: 0, a: 0 }
    }

    /// Create a red color
    pub fn red(alpha: u8) -> Self {
        Self { r: 255, g: 0, b: 0, a: alpha }
    }

    /// Create a green color
    pub fn green(alpha: u8) -> Self {
        Self { r: 0, g: 255, b: 0, a: alpha }
    }

    /// Create a blue color
    pub fn blue(alpha: u8) -> Self {
        Self { r: 0, g: 0, b: 255, a: alpha }
    }

    /// Create a white color
    pub fn white(alpha: u8) -> Self {
        Self { r: 255, g: 255, b: 255, a: alpha }
    }

    /// Convert to BGRA byte array
    pub fn to_bgra_bytes(self) -> [u8; 4] {
        [self.b, self.g, self.r, self.a]
    }
}

/// Represents a 2D point
#[derive(Debug, Clone, Copy)]
pub struct Point {
    pub x: i32,
    pub y: i32,
}

impl Point {
    pub fn new(x: i32, y: i32) -> Self {
        Self { x, y }
    }
}

/// Represents a rectangle
#[derive(Debug, Clone, Copy)]
pub struct Rect {
    pub x: i32,
    pub y: i32,
    pub width: u32,
    pub height: u32,
}

impl Rect {
    pub fn new(x: i32, y: i32, width: u32, height: u32) -> Self {
        Self { x, y, width, height }
    }
}

/// Drawing context for the overlay
pub struct DrawingContext<'a> {
    canvas: &'a mut [u8],
    width: u32,
    height: u32,
}

impl<'a> DrawingContext<'a> {
    /// Create a new drawing context
    pub fn new(canvas: &'a mut [u8], width: u32, height: u32) -> Self {
        Self { canvas, width, height }
    }

    /// Clear the entire canvas with a color
    pub fn clear(&mut self, color: Color) {
        let color_bytes = color.to_bgra_bytes();
        self.canvas.chunks_exact_mut(4).for_each(|chunk| {
            chunk.copy_from_slice(&color_bytes);
        });
    }

    /// Set a pixel at the given coordinates
    pub fn set_pixel(&mut self, x: i32, y: i32, color: Color) {
        if x < 0 || y < 0 || x >= self.width as i32 || y >= self.height as i32 {
            return; // Out of bounds
        }

        let index = ((y as u32 * self.width + x as u32) * 4) as usize;
        if index + 3 < self.canvas.len() {
            let color_bytes = color.to_bgra_bytes();
            self.canvas[index..index + 4].copy_from_slice(&color_bytes);
        }
    }

    /// Draw a horizontal line
    pub fn draw_horizontal_line(&mut self, x1: i32, x2: i32, y: i32, color: Color) {
        let start_x = x1.min(x2);
        let end_x = x1.max(x2);
        
        for x in start_x..=end_x {
            self.set_pixel(x, y, color);
        }
    }

    /// Draw a vertical line
    pub fn draw_vertical_line(&mut self, x: i32, y1: i32, y2: i32, color: Color) {
        let start_y = y1.min(y2);
        let end_y = y1.max(y2);
        
        for y in start_y..=end_y {
            self.set_pixel(x, y, color);
        }
    }

    /// Draw a rectangle outline
    pub fn draw_rect_outline(&mut self, rect: Rect, color: Color) {
        // Top edge
        self.draw_horizontal_line(rect.x, rect.x + rect.width as i32 - 1, rect.y, color);
        // Bottom edge
        self.draw_horizontal_line(rect.x, rect.x + rect.width as i32 - 1, rect.y + rect.height as i32 - 1, color);
        // Left edge
        self.draw_vertical_line(rect.x, rect.y, rect.y + rect.height as i32 - 1, color);
        // Right edge
        self.draw_vertical_line(rect.x + rect.width as i32 - 1, rect.y, rect.y + rect.height as i32 - 1, color);
    }

    /// Draw a filled rectangle
    pub fn draw_rect_filled(&mut self, rect: Rect, color: Color) {
        for y in rect.y..rect.y + rect.height as i32 {
            self.draw_horizontal_line(rect.x, rect.x + rect.width as i32 - 1, y, color);
        }
    }

    /// Draw a crosshair at the center of the screen
    pub fn draw_crosshair(&mut self, size: u32, thickness: u32, color: Color) {
        let center_x = self.width as i32 / 2;
        let center_y = self.height as i32 / 2;
        let half_size = size as i32 / 2;

        // Horizontal line
        for t in 0..thickness {
            let offset = t as i32 - thickness as i32 / 2;
            self.draw_horizontal_line(
                center_x - half_size,
                center_x + half_size,
                center_y + offset,
                color,
            );
        }

        // Vertical line
        for t in 0..thickness {
            let offset = t as i32 - thickness as i32 / 2;
            self.draw_vertical_line(
                center_x + offset,
                center_y - half_size,
                center_y + half_size,
                color,
            );
        }
    }

    /// Draw an ESP box (typically used for highlighting players)
    pub fn draw_esp_box(&mut self, rect: Rect, color: Color, thickness: u32) {
        for i in 0..thickness {
            let offset = i as i32;
            let expanded_rect = Rect::new(
                rect.x - offset,
                rect.y - offset,
                rect.width + 2 * i,
                rect.height + 2 * i,
            );
            self.draw_rect_outline(expanded_rect, color);
        }
    }
}
