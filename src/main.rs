//! CS2 External Cheat - Overlay Window
//!
//! This module creates a transparent fullscreen overlay window using Wayland's layer shell protocol.
//! The overlay is designed to be used for displaying cheat information over the CS2 game window.
//!
//! IMPORTANT: This cheat is for educational purposes only and should only be used in private
//! games with bots and with the -insecure flag to disable VAC.
//!
//! The overlay uses:
//! - Layer shell protocol for positioning above all other windows
//! - Transparent rendering for see-through overlay
//! - No keyboard interactivity for click-through behavior
//! - Dynamic sizing to match screen dimensions

use std::num::NonZeroU32;

mod drawing;

use smithay_client_toolkit::{
    compositor::{CompositorHandler, CompositorState},
    delegate_compositor, delegate_layer, delegate_output, delegate_registry, delegate_shm,
    output::{OutputHandler, OutputState},
    registry::{ProvidesRegistryState, RegistryState},
    registry_handlers,
    shell::{
        wlr_layer::{
            Anchor, KeyboardInteractivity, Layer, LayerShell, Layer<PERSON><PERSON><PERSON><PERSON>ler, <PERSON>er<PERSON>ur<PERSON>,
            LayerSurfaceConfigure,
        },
        WaylandSurface,
    },
    shm::{slot::<PERSON><PERSON><PERSON><PERSON>, <PERSON>hm, <PERSON>hm<PERSON><PERSON><PERSON>},
};
use wayland_client::{
    globals::registry_queue_init,
    protocol::{wl_output, wl_shm, wl_surface},
    Connection, QueueHandle,
};

fn main() {
    env_logger::init();

    // All Wayland apps start by connecting the compositor (server).
    let conn = Connection::connect_to_env().unwrap();

    // Enumerate the list of globals to get the protocols the server implements.
    let (globals, mut event_queue) = registry_queue_init(&conn).unwrap();
    let qh = event_queue.handle();

    // The compositor (not to be confused with the server which is commonly called the compositor) allows
    // configuring surfaces to be presented.
    let compositor = CompositorState::bind(&globals, &qh).expect("wl_compositor is not available");

    // This app uses the wlr layer shell, which may not be available with every compositor.
    let layer_shell = LayerShell::bind(&globals, &qh).expect("layer shell is not available");

    // Since we are not using the GPU in this example, we use wl_shm to allow software rendering to a buffer
    // we share with the compositor process.
    let shm = Shm::bind(&globals, &qh).expect("wl_shm is not available");

    // A layer surface is created from a surface.
    let surface = compositor.create_surface(&qh);

    // And then we create the layer shell.
    let layer = layer_shell.create_layer_surface(&qh, surface, Layer::Overlay, Some("cs2_overlay"), None);

    // Configure the layer surface for a fullscreen overlay
    // - Anchor to all edges to cover the entire screen
    // - No keyboard interactivity to allow click-through
    // - Set to overlay layer for highest z-order
    // - Don't reserve compositor space
    layer.set_anchor(Anchor::TOP | Anchor::BOTTOM | Anchor::LEFT | Anchor::RIGHT);
    layer.set_keyboard_interactivity(KeyboardInteractivity::None);
    layer.set_size(0, 0); // 0,0 means use the full output size
    layer.set_exclusive_zone(-1); // Don't reserve compositor space

    // TODO: Create an empty input region to make the overlay click-through
    // For now, we'll rely on the transparent pixels and keyboard interactivity settings

    // In order for the layer surface to be mapped, we need to perform an initial commit with no attached
    // buffer. For more info, see WaylandSurface::commit
    //
    // The compositor will respond with an initial configure that we can then use to present to the layer
    // surface with the correct options.
    layer.commit();

    // We don't know how large the window will be yet, so lets assume a reasonable size for the
    // initial memory allocation. This will be resized when we get the actual screen dimensions.
    let pool = SlotPool::new(1920 * 1080 * 4, &shm).expect("Failed to create pool");

    let mut cs2_overlay = CS2Overlay {
        registry_state: RegistryState::new(&globals),
        output_state: OutputState::new(&globals, &qh),
        shm,
        exit: false,
        first_configure: true,
        pool,
        width: 1920,
        height: 1080,
        layer,
    };

    // We don't draw immediately, the configure will notify us when to first draw.
    loop {
        event_queue.blocking_dispatch(&mut cs2_overlay).unwrap();

        if cs2_overlay.exit {
            break;
        }
    }
}

/// CS2 External Cheat Overlay
///
/// This struct represents the main overlay window for the CS2 external cheat.
/// It creates a transparent fullscreen overlay using Wayland's layer shell protocol.
/// The overlay is positioned on the highest layer (Overlay) and is configured to be
/// click-through and non-interactive.
struct CS2Overlay {
    registry_state: RegistryState,
    output_state: OutputState,
    shm: Shm,
    exit: bool,
    first_configure: bool,
    pool: SlotPool,
    width: u32,
    height: u32,
    layer: LayerSurface,
}

impl CompositorHandler for CS2Overlay {
    fn scale_factor_changed(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _surface: &wl_surface::WlSurface,
        _new_factor: i32,
    ) {
        // Not needed for this example.
    }

    fn transform_changed(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _surface: &wl_surface::WlSurface,
        _new_transform: wl_output::Transform,
    ) {
        // Not needed for this example.
    }

    fn frame(
        &mut self,
        _conn: &Connection,
        qh: &QueueHandle<Self>,
        _surface: &wl_surface::WlSurface,
        _time: u32,
    ) {
        self.draw(qh);
    }
}

impl OutputHandler for CS2Overlay {
    fn output_state(&mut self) -> &mut OutputState {
        &mut self.output_state
    }

    fn new_output(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _output: wl_output::WlOutput,
    ) {
    }

    fn update_output(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _output: wl_output::WlOutput,
    ) {
    }

    fn output_destroyed(
        &mut self,
        _conn: &Connection,
        _qh: &QueueHandle<Self>,
        _output: wl_output::WlOutput,
    ) {
    }
}

impl LayerShellHandler for CS2Overlay {
    fn closed(&mut self, _conn: &Connection, _qh: &QueueHandle<Self>, _layer: &LayerSurface) {
        self.exit = true;
    }

    fn configure(
        &mut self,
        _conn: &Connection,
        qh: &QueueHandle<Self>,
        _layer: &LayerSurface,
        configure: LayerSurfaceConfigure,
        _serial: u32,
    ) {
        // Update dimensions based on compositor's response
        // If the compositor doesn't specify a size, use reasonable defaults
        self.width = NonZeroU32::new(configure.new_size.0).map_or(1920, NonZeroU32::get);
        self.height = NonZeroU32::new(configure.new_size.1).map_or(1080, NonZeroU32::get);

        // Initiate the first draw after initial configuration
        if self.first_configure {
            self.first_configure = false;
            self.draw(qh);
        }
    }
}

impl ShmHandler for CS2Overlay {
    fn shm_state(&mut self) -> &mut Shm {
        &mut self.shm
    }
}

impl CS2Overlay {
    pub fn draw(&mut self, qh: &QueueHandle<Self>) {
        let width = self.width;
        let height = self.height;
        let stride = self.width as i32 * 4;

        // Ensure the pool is large enough for the current dimensions
        let required_size = (width * height * 4) as usize;
        if self.pool.len() < required_size {
            self.pool = SlotPool::new(required_size, &self.shm).expect("Failed to resize pool");
        }

        let (buffer, canvas) = self
            .pool
            .create_buffer(width as i32, height as i32, stride, wl_shm::Format::Argb8888)
            .expect("create buffer");

        // Create a fully transparent overlay and optionally add cheat graphics
        // For demonstration, we'll draw a simple crosshair (uncomment to enable)

        // Clear with transparent background
        canvas.chunks_exact_mut(4).for_each(|chunk| {
            let array: &mut [u8; 4] = chunk.try_into().unwrap();
            *array = [0, 0, 0, 0]; // Fully transparent (B, G, R, A)
        });

        // Example: Draw a crosshair (uncomment the lines below to enable)
        /*
        let mut ctx = drawing::DrawingContext::new(canvas, width, height);
        ctx.draw_crosshair(20, 2, drawing::Color::white(128));

        // Example: Draw an ESP box
        let esp_box = drawing::Rect::new(100, 100, 50, 100);
        ctx.draw_esp_box(esp_box, drawing::Color::red(180), 2);
        */

        // Damage the entire window to indicate it needs to be redrawn
        self.layer.wl_surface().damage_buffer(0, 0, width as i32, height as i32);

        // Request our next frame callback
        self.layer.wl_surface().frame(qh, self.layer.wl_surface().clone());

        // Attach the buffer and commit to present the frame
        buffer.attach_to(self.layer.wl_surface()).expect("buffer attach");
        self.layer.commit();
    }
}

// Delegate implementations
delegate_compositor!(CS2Overlay);
delegate_output!(CS2Overlay);
delegate_shm!(CS2Overlay);
delegate_layer!(CS2Overlay);
delegate_registry!(CS2Overlay);

impl ProvidesRegistryState for CS2Overlay {
    fn registry(&mut self) -> &mut RegistryState {
        &mut self.registry_state
    }
    registry_handlers![OutputState];
}