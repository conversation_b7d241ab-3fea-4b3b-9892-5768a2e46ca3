# CS2 External Cheat - Overlay Window

This is the overlay window component of the CS2 external cheat project. It creates a transparent fullscreen overlay using Wayland's layer shell protocol.

## ⚠️ IMPORTANT DISCLAIMER

**THIS CHEAT IS FOR EDUCATIONAL PURPOSES ONLY!**

- Only use this in private games with bots
- Always run CS2 with the `-insecure` flag to disable VAC
- Never use this in public matchmaking
- This is for learning how cheats and the game work

## Features

- **Transparent Overlay**: Creates a fully transparent overlay that doesn't interfere with the game
- **Click-through**: The overlay doesn't capture mouse or keyboard input
- **Fullscreen**: Automatically covers the entire screen
- **Layer Shell**: Uses Wayland's layer shell protocol for proper positioning above all windows
- **Dynamic Sizing**: Automatically adapts to screen resolution changes

## Requirements

- Linux with Wayland compositor that supports layer shell protocol (e.g., Sway, Hyprland, KDE Plasma on Wayland)
- Rust toolchain
- CS2 running with `-insecure` flag

## Building and Running

```bash
# Build the overlay
cargo build --release

# Run the overlay
cargo run --release
```

## Architecture

The overlay uses the following Wayland protocols:
- `wl_compositor` - For creating surfaces
- `wl_shm` - For shared memory buffers
- `zwlr_layer_shell_v1` - For layer shell positioning

### Key Components

- **CS2Overlay struct**: Main application state
- **Layer Surface**: Wayland layer shell surface for overlay positioning
- **Shared Memory Pool**: For rendering transparent frames
- **Event Loop**: Handles Wayland events and redraws

## Usage with CS2

1. Start CS2 with the `-insecure` flag:
   ```bash
   steam steam://rungameid/730 -insecure
   ```

2. Run the overlay in a separate terminal:
   ```bash
   cargo run --release
   ```

3. The overlay will appear as a transparent layer over all windows

## Development

The overlay is currently set up as a transparent window. To add cheat functionality:

1. Modify the `draw()` function in `src/main.rs`
2. Add your rendering code where the comment indicates
3. Use the canvas buffer to draw ESP boxes, crosshairs, etc.

## Supported Compositors

- ✅ Sway
- ✅ Hyprland  
- ✅ KDE Plasma (Wayland)
- ✅ GNOME (with layer shell extension)
- ❌ X11 (not supported - Wayland only)

## Troubleshooting

**"layer shell is not available"**: Your compositor doesn't support the layer shell protocol. Try using a different Wayland compositor.

**Overlay not visible**: The overlay is fully transparent by default. This is intentional for the base implementation.

**Permission denied**: Make sure you're running on Wayland and have proper permissions.
