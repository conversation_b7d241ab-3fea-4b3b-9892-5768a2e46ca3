#!/bin/bash

# CS2 Overlay Test Script
# This script tests the overlay window functionality

echo "CS2 External Cheat - Overlay Test"
echo "=================================="
echo ""
echo "⚠️  IMPORTANT: This is for educational purposes only!"
echo "   Only use with CS2 running with -insecure flag in private games with bots."
echo ""

# Check if we're running on Wayland
if [ -z "$WAYLAND_DISPLAY" ]; then
    echo "❌ Error: This overlay requires Wayland. WAYLAND_DISPLAY is not set."
    echo "   Please run this on a Wayland compositor (Sway, Hyprland, KDE Plasma on Wayland, etc.)"
    exit 1
fi

echo "✅ Wayland detected: $WAYLAND_DISPLAY"

# Check if Rust is installed
if ! command -v cargo &> /dev/null; then
    echo "❌ Error: Rust/Cargo not found. Please install Rust first."
    echo "   Visit: https://rustup.rs/"
    exit 1
fi

echo "✅ Rust/Cargo found"

# Build the overlay
echo ""
echo "Building overlay..."
if cargo build --release; then
    echo "✅ Build successful"
else
    echo "❌ Build failed"
    exit 1
fi

echo ""
echo "🚀 Starting overlay test..."
echo "   The overlay will run for 5 seconds, then automatically exit."
echo "   You should see a transparent overlay covering your screen."
echo "   Press Ctrl+C to exit early if needed."
echo ""

# Run the overlay with a timeout
timeout 5 cargo run --release

echo ""
echo "✅ Overlay test completed!"
echo ""
echo "Next steps:"
echo "1. To enable visual elements, edit src/main.rs and uncomment the drawing examples"
echo "2. To add CS2-specific functionality, integrate with game memory reading"
echo "3. Always remember to run CS2 with -insecure flag for testing"
echo ""
echo "Happy learning! 🎮"
